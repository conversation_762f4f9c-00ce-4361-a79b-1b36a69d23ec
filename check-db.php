<?php
// Simple database connection test
echo "<h2>Database Connection Test</h2>";

echo "<h3>Testing MySQL Connection...</h3>";

try {
    // Test basic MySQL connection
    $pdo = new PDO("mysql:host=localhost", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✓ MySQL server connection: <strong style='color: green;'>SUCCESS</strong><br>";
    
    // Check if database exists
    $stmt = $pdo->query("SHOW DATABASES LIKE 'southsafari'");
    if ($stmt->rowCount() > 0) {
        echo "✓ Database 'southsafari': <strong style='color: green;'>EXISTS</strong><br>";
    } else {
        echo "✗ Database 'southsafari': <strong style='color: red;'>NOT FOUND</strong><br>";
        echo "Creating database...<br>";
        $pdo->exec("CREATE DATABASE southsafari");
        echo "✓ Database 'southsafari': <strong style='color: green;'>CREATED</strong><br>";
    }
    
    // Test connection to southsafari database
    $pdo = new PDO("mysql:host=localhost;dbname=southsafari", "root", "");
    echo "✓ Connection to 'southsafari' database: <strong style='color: green;'>SUCCESS</strong><br>";
    
    echo "<br><a href='setup.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Run Setup Now</a>";
    
} catch (PDOException $e) {
    echo "✗ Database connection: <strong style='color: red;'>FAILED</strong><br>";
    echo "Error: " . $e->getMessage() . "<br><br>";
    
    echo "<h3>Troubleshooting Steps:</h3>";
    echo "<ol>";
    echo "<li><strong>Check XAMPP Control Panel:</strong><br>";
    echo "   - Make sure Apache is running (green)<br>";
    echo "   - Make sure MySQL is running (green)<br></li>";
    echo "<li><strong>If MySQL won't start:</strong><br>";
    echo "   - Click 'Logs' next to MySQL in XAMPP<br>";
    echo "   - Check for port conflicts (usually port 3306)<br>";
    echo "   - Try restarting XAMPP as administrator<br></li>";
    echo "<li><strong>Alternative ports:</strong><br>";
    echo "   - Some systems use port 3307 instead of 3306<br>";
    echo "   - Check XAMPP MySQL configuration<br></li>";
    echo "</ol>";
    
    echo "<h3>Quick Fixes:</h3>";
    echo "<ul>";
    echo "<li>Restart XAMPP completely</li>";
    echo "<li>Run XAMPP as Administrator</li>";
    echo "<li>Check if Skype or other software is using port 3306</li>";
    echo "<li>Try accessing phpMyAdmin: <a href='http://localhost/phpmyadmin' target='_blank'>http://localhost/phpmyadmin</a></li>";
    echo "</ul>";
}
?>

<style>
body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
h2, h3 { color: #333; }
</style>
