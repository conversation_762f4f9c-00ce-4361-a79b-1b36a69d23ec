<?php
require_once 'config/config.php';

$page_title = 'Current Projects';
$page_description = 'Discover active project opportunities seeking skilled developers for partnerships in the South African market.';

// Get projects from database
try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Handle search and filter
    $search = isset($_GET['search']) ? sanitize_input($_GET['search']) : '';
    $filter = isset($_GET['filter']) ? sanitize_input($_GET['filter']) : 'all';
    
    // Build query
    $query = "SELECT * FROM projects WHERE status = 'seeking'";
    $params = [];
    
    if (!empty($search)) {
        $query .= " AND (title LIKE ? OR description LIKE ? OR skills_required LIKE ?)";
        $searchTerm = "%$search%";
        $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
    }
    
    $query .= " ORDER BY created_at DESC";
    
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    $projects = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch(Exception $e) {
    $projects = [];
}

include 'includes/header.php';
?>

<!-- Page Header -->
<section class="section" style="background: #f8f9fa; padding: 2rem 0;">
    <div class="container">
        <h1 class="section-title">Current Projects Seeking Developers</h1>
        <p class="section-subtitle">Explore partnership opportunities and apply for projects that match your skills and interests</p>
    </div>
</section>

<!-- Search and Filter Section -->
<section class="section" style="padding: 2rem 0;">
    <div class="container">
        <div class="search-filter-bar" style="display: flex; gap: 1rem; margin-bottom: 2rem; flex-wrap: wrap; align-items: center;">
            <div style="flex: 1; min-width: 300px;">
                <input type="text" 
                       class="search-input" 
                       data-target="projects"
                       placeholder="Search projects by title, description, or skills..." 
                       value="<?php echo htmlspecialchars($search); ?>"
                       style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px; font-size: 1rem;">
            </div>
            <div class="filter-buttons" style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                <button class="filter-btn btn <?php echo $filter === 'all' ? 'btn-primary' : 'btn-secondary'; ?>" 
                        data-filter="all" data-target="projects" style="padding: 0.5rem 1rem;">All Projects</button>
                <button class="filter-btn btn <?php echo $filter === 'web' ? 'btn-primary' : 'btn-secondary'; ?>" 
                        data-filter="web" data-target="projects" style="padding: 0.5rem 1rem;">Web Development</button>
                <button class="filter-btn btn <?php echo $filter === 'mobile' ? 'btn-primary' : 'btn-secondary'; ?>" 
                        data-filter="mobile" data-target="projects" style="padding: 0.5rem 1rem;">Mobile Apps</button>
                <button class="filter-btn btn <?php echo $filter === 'ecommerce' ? 'btn-primary' : 'btn-secondary'; ?>" 
                        data-filter="ecommerce" data-target="projects" style="padding: 0.5rem 1rem;">E-commerce</button>
            </div>
        </div>
    </div>
</section>

<!-- Projects Grid -->
<section class="section">
    <div class="container">
        <?php if (empty($projects)): ?>
            <div class="text-center" style="padding: 3rem 0;">
                <i class="fas fa-search" style="font-size: 3rem; color: #ccc; margin-bottom: 1rem;"></i>
                <h3>No Projects Found</h3>
                <p>There are currently no projects matching your criteria. Please check back later or adjust your search.</p>
                <a href="contact.php" class="btn btn-primary" style="margin-top: 1rem;">Contact Us for Updates</a>
            </div>
        <?php else: ?>
            <div class="grid grid-2" id="projects-grid">
                <?php foreach ($projects as $project): ?>
                    <div class="card project-card" 
                         data-searchable="projects" 
                         data-filterable="projects"
                         data-category="<?php echo strtolower(strpos($project['title'], 'Mobile') !== false ? 'mobile' : (strpos($project['title'], 'E-commerce') !== false ? 'ecommerce' : 'web')); ?>">
                        
                        <div class="card-header">
                            <h3 class="card-title"><?php echo htmlspecialchars($project['title']); ?></h3>
                            <div class="card-meta">
                                <span class="project-status">
                                    <i class="fas fa-circle" style="color: #28a745; font-size: 0.5rem;"></i>
                                    Seeking Developers
                                </span>
                                <span style="margin-left: 1rem; color: #888;">
                                    <i class="fas fa-calendar"></i>
                                    Posted <?php echo format_date($project['created_at']); ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="card-content">
                            <p><?php echo truncate_text($project['description'], 120); ?></p>
                            
                            <div style="margin: 1rem 0;">
                                <strong>Skills Required:</strong>
                                <div class="skills-tags" style="margin-top: 0.5rem;">
                                    <?php 
                                    $skills = explode(',', $project['skills_required']);
                                    foreach ($skills as $skill): 
                                        $skill = trim($skill);
                                    ?>
                                        <span class="skill-tag" style="display: inline-block; background: #f0f0f0; padding: 0.25rem 0.5rem; margin: 0.25rem 0.25rem 0.25rem 0; border-radius: 3px; font-size: 0.85rem;">
                                            <?php echo htmlspecialchars($skill); ?>
                                        </span>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            
                            <div style="margin: 1rem 0;">
                                <strong>Partnership Terms:</strong>
                                <p style="margin-top: 0.5rem; color: #666; font-size: 0.9rem;">
                                    <?php echo truncate_text($project['partnership_terms'], 80); ?>
                                </p>
                            </div>
                        </div>
                        
                        <div class="card-footer">
                            <button class="btn btn-primary apply-btn" 
                                    onclick="openApplicationModal(<?php echo $project['id']; ?>, '<?php echo htmlspecialchars($project['title']); ?>')">
                                Apply for This Project
                            </button>
                            <button class="btn btn-secondary" 
                                    onclick="showProjectDetails(<?php echo $project['id']; ?>)">
                                View Details
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Application Modal -->
<div id="application-modal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div class="modal-content" style="background: white; margin: 5% auto; padding: 2rem; width: 90%; max-width: 600px; border-radius: 10px; position: relative;">
        <span class="close" onclick="closeApplicationModal()" style="position: absolute; top: 1rem; right: 1rem; font-size: 1.5rem; cursor: pointer;">&times;</span>
        
        <h2 id="modal-title">Apply for Project</h2>
        <p style="color: #666; margin-bottom: 2rem;">Fill out the form below to apply for this partnership opportunity.</p>
        
        <form id="application-form" class="application-form">
            <input type="hidden" id="project-id" name="project_id">
            
            <div style="margin-bottom: 1rem;">
                <label for="applicant-name" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Full Name *</label>
                <input type="text" id="applicant-name" name="name" required 
                       style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px;">
            </div>
            
            <div style="margin-bottom: 1rem;">
                <label for="applicant-email" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Email Address *</label>
                <input type="email" id="applicant-email" name="email" required 
                       style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px;">
            </div>
            
            <div style="margin-bottom: 1rem;">
                <label for="applicant-country" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Country *</label>
                <select id="applicant-country" name="country" required 
                        style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px;">
                    <option value="">Select your country</option>
                    <option value="Pakistan">Pakistan</option>
                    <option value="India">India</option>
                    <option value="Bangladesh">Bangladesh</option>
                    <option value="Sri Lanka">Sri Lanka</option>
                    <option value="Nepal">Nepal</option>
                    <option value="Other">Other</option>
                </select>
            </div>
            
            <div style="margin-bottom: 1rem;">
                <label for="experience-level" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Experience Level *</label>
                <select id="experience-level" name="experience_level" required 
                        style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px;">
                    <option value="">Select experience level</option>
                    <option value="junior">Junior (1-2 years)</option>
                    <option value="mid">Mid-level (3-5 years)</option>
                    <option value="senior">Senior (5+ years)</option>
                    <option value="expert">Expert (10+ years)</option>
                </select>
            </div>
            
            <div style="margin-bottom: 1rem;">
                <label for="skills" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Relevant Skills *</label>
                <textarea id="skills" name="skills" required rows="3" 
                          placeholder="List your relevant skills and technologies..."
                          style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px; resize: vertical;"></textarea>
            </div>
            
            <div style="margin-bottom: 1rem;">
                <label for="portfolio-url" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Portfolio URL</label>
                <input type="url" id="portfolio-url" name="portfolio_url" 
                       placeholder="https://your-portfolio.com"
                       style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px;">
            </div>
            
            <div style="margin-bottom: 1rem;">
                <label for="application-message" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Why are you interested in this project? *</label>
                <textarea id="application-message" name="message" required rows="4" 
                          placeholder="Tell us why you're the right fit for this project..."
                          style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px; resize: vertical;"></textarea>
            </div>
            
            <div style="margin-bottom: 2rem;">
                <label for="cv-file" style="display: block; margin-bottom: 0.5rem; font-weight: 500;">Upload CV/Resume</label>
                <input type="file" id="cv-file" name="cv_file" accept=".pdf,.doc,.docx" 
                       style="width: 100%; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px;">
                <small style="color: #666;">Accepted formats: PDF, DOC, DOCX (Max 5MB)</small>
            </div>
            
            <div style="text-align: right;">
                <button type="button" onclick="closeApplicationModal()" class="btn btn-secondary" style="margin-right: 1rem;">Cancel</button>
                <button type="submit" class="btn btn-primary">Submit Application</button>
            </div>
        </form>
    </div>
</div>

<script>
function openApplicationModal(projectId, projectTitle) {
    document.getElementById('project-id').value = projectId;
    document.getElementById('modal-title').textContent = 'Apply for: ' + projectTitle;
    document.getElementById('application-modal').style.display = 'block';
    document.body.style.overflow = 'hidden';
}

function closeApplicationModal() {
    document.getElementById('application-modal').style.display = 'none';
    document.body.style.overflow = 'auto';
    document.getElementById('application-form').reset();
}

function showProjectDetails(projectId) {
    // This would typically open a detailed view or redirect to a project detail page
    alert('Project details functionality will be implemented. Project ID: ' + projectId);
}

// Close modal when clicking outside
document.getElementById('application-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeApplicationModal();
    }
});
</script>

<?php include 'includes/footer.php'; ?>
