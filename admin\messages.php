<?php
session_start();
require_once '../config/config.php';

// Check if admin is logged in
if (!is_admin_logged_in()) {
    redirect('login.php');
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Handle actions
    $message = '';
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['action'];
        
        if ($action === 'mark_read') {
            $id = (int)$_POST['id'];
            $stmt = $db->prepare("UPDATE messages SET status = 'read' WHERE id = ?");
            $stmt->execute([$id]);
            $message = "Message marked as read!";
        } elseif ($action === 'mark_unread') {
            $id = (int)$_POST['id'];
            $stmt = $db->prepare("UPDATE messages SET status = 'unread' WHERE id = ?");
            $stmt->execute([$id]);
            $message = "Message marked as unread!";
        } elseif ($action === 'delete') {
            $id = (int)$_POST['id'];
            $stmt = $db->prepare("DELETE FROM messages WHERE id = ?");
            $stmt->execute([$id]);
            $message = "Message deleted successfully!";
        }
    }
    
    // Get messages
    $filter = $_GET['filter'] ?? 'all';
    $search = $_GET['search'] ?? '';
    
    $query = "SELECT * FROM messages WHERE 1=1";
    $params = [];
    
    if ($filter === 'unread') {
        $query .= " AND status = 'unread'";
    } elseif ($filter === 'read') {
        $query .= " AND status = 'read'";
    }
    
    if (!empty($search)) {
        $query .= " AND (name LIKE ? OR email LIKE ? OR subject LIKE ? OR message LIKE ?)";
        $searchTerm = "%$search%";
        $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
    }
    
    $query .= " ORDER BY created_at DESC";
    
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    $messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get counts
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM messages WHERE status = 'unread'");
    $stmt->execute();
    $unread_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM messages");
    $stmt->execute();
    $total_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
} catch (Exception $e) {
    $message = "Error: " . $e->getMessage();
    $messages = [];
    $unread_count = 0;
    $total_count = 0;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Messages - South Safari Admin</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .admin-header {
            background: #1a1a1a;
            color: white;
            padding: 1rem 0;
        }
        .admin-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 2rem;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .nav-links a:hover, .nav-links a.active {
            background: rgba(255,255,255,0.1);
        }
        .admin-main {
            padding: 2rem 0;
            min-height: calc(100vh - 200px);
        }
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }
        .filters {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            align-items: center;
        }
        .filter-btn {
            padding: 0.5rem 1rem;
            border: 2px solid #ddd;
            background: white;
            border-radius: 25px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s;
        }
        .filter-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        .search-box {
            flex: 1;
            max-width: 300px;
        }
        .search-box input {
            width: 100%;
            padding: 0.5rem 1rem;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 1rem;
        }
        .messages-list {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .message-item {
            padding: 1.5rem;
            border-bottom: 1px solid #eee;
            transition: background 0.3s;
        }
        .message-item:hover {
            background: #f8f9fa;
        }
        .message-item.unread {
            background: #f0f8ff;
            border-left: 4px solid #667eea;
        }
        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }
        .message-info h4 {
            margin: 0 0 0.25rem 0;
            color: #1a1a1a;
        }
        .message-info p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }
        .message-meta {
            text-align: right;
            font-size: 0.8rem;
            color: #666;
        }
        .message-subject {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
        }
        .message-content {
            color: #555;
            line-height: 1.6;
            margin-bottom: 1rem;
        }
        .message-actions {
            display: flex;
            gap: 0.5rem;
        }
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 0.9rem;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .status-unread {
            background: #fff3cd;
            color: #856404;
        }
        .status-read {
            background: #d4edda;
            color: #155724;
        }
        .message-alert {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        .message-alert.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .stats-bar {
            display: flex;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        .stat-item {
            background: white;
            padding: 1rem 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <header class="admin-header">
        <div class="container">
            <nav class="admin-nav">
                <h1><i class="fas fa-mountain"></i> South Safari Admin</h1>
                <div class="nav-links">
                    <a href="dashboard.php">Dashboard</a>
                    <a href="projects.php">Projects</a>
                    <a href="developers.php">Developers</a>
                    <a href="messages.php" class="active">Messages</a>
                    <a href="../index.php" target="_blank">View Site</a>
                    <a href="logout.php">Logout</a>
                </div>
            </nav>
        </div>
    </header>

    <main class="admin-main">
        <div class="container">
            <?php if ($message): ?>
                <div class="message-alert success">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <div class="page-header">
                <h2>Messages</h2>
            </div>

            <div class="stats-bar">
                <div class="stat-item">
                    <div class="stat-number"><?php echo $total_count; ?></div>
                    <div class="stat-label">Total Messages</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?php echo $unread_count; ?></div>
                    <div class="stat-label">Unread Messages</div>
                </div>
            </div>

            <div class="filters">
                <a href="?filter=all<?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>" 
                   class="filter-btn <?php echo $filter === 'all' ? 'active' : ''; ?>">
                    All Messages
                </a>
                <a href="?filter=unread<?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>" 
                   class="filter-btn <?php echo $filter === 'unread' ? 'active' : ''; ?>">
                    Unread (<?php echo $unread_count; ?>)
                </a>
                <a href="?filter=read<?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>" 
                   class="filter-btn <?php echo $filter === 'read' ? 'active' : ''; ?>">
                    Read
                </a>
                
                <div class="search-box">
                    <form method="GET">
                        <input type="hidden" name="filter" value="<?php echo htmlspecialchars($filter); ?>">
                        <input type="text" name="search" placeholder="Search messages..." 
                               value="<?php echo htmlspecialchars($search); ?>">
                    </form>
                </div>
            </div>

            <div class="messages-list">
                <?php foreach ($messages as $msg): ?>
                    <div class="message-item <?php echo $msg['status']; ?>">
                        <div class="message-header">
                            <div class="message-info">
                                <h4><?php echo htmlspecialchars($msg['name']); ?></h4>
                                <p><?php echo htmlspecialchars($msg['email']); ?></p>
                            </div>
                            <div class="message-meta">
                                <span class="status-badge status-<?php echo $msg['status']; ?>">
                                    <?php echo ucfirst($msg['status']); ?>
                                </span>
                                <br>
                                <small><?php echo format_date($msg['created_at']); ?></small>
                            </div>
                        </div>
                        
                        <div class="message-subject">
                            Subject: <?php echo htmlspecialchars($msg['subject']); ?>
                        </div>
                        
                        <div class="message-content">
                            <?php echo nl2br(htmlspecialchars($msg['message'])); ?>
                        </div>
                        
                        <div class="message-actions">
                            <?php if ($msg['status'] === 'unread'): ?>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="mark_read">
                                    <input type="hidden" name="id" value="<?php echo $msg['id']; ?>">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-check"></i> Mark as Read
                                    </button>
                                </form>
                            <?php else: ?>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="mark_unread">
                                    <input type="hidden" name="id" value="<?php echo $msg['id']; ?>">
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-undo"></i> Mark as Unread
                                    </button>
                                </form>
                            <?php endif; ?>
                            
                            <a href="mailto:<?php echo htmlspecialchars($msg['email']); ?>?subject=Re: <?php echo urlencode($msg['subject']); ?>" 
                               class="btn btn-primary">
                                <i class="fas fa-reply"></i> Reply
                            </a>
                            
                            <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this message?');">
                                <input type="hidden" name="action" value="delete">
                                <input type="hidden" name="id" value="<?php echo $msg['id']; ?>">
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                            </form>
                        </div>
                    </div>
                <?php endforeach; ?>
                
                <?php if (empty($messages)): ?>
                    <div style="text-align: center; padding: 3rem; color: #666;">
                        <i class="fas fa-envelope" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                        <h3>No Messages Found</h3>
                        <p>
                            <?php if (!empty($search)): ?>
                                No messages match your search criteria.
                            <?php elseif ($filter === 'unread'): ?>
                                No unread messages at the moment.
                            <?php else: ?>
                                No messages have been received yet.
                            <?php endif; ?>
                        </p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </main>
</body>
</html>
