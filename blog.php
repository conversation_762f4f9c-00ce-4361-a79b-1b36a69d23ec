<?php
require_once 'config/config.php';

$page_title = 'Blog';
$page_description = 'Stay updated with the latest news, insights, and success stories from South Safari partnerships and the South African tech market.';

// Get blog posts from database
try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Handle pagination
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $offset = ($page - 1) * POSTS_PER_PAGE;
    
    // Handle category filter
    $category = isset($_GET['category']) ? sanitize_input($_GET['category']) : '';
    
    // Build query
    $whereClause = "WHERE status = 'published'";
    $params = [];
    
    if (!empty($category)) {
        $whereClause .= " AND category = ?";
        $params[] = $category;
    }
    
    // Get total count for pagination
    $countQuery = "SELECT COUNT(*) as count FROM blog_posts $whereClause";
    $countStmt = $db->prepare($countQuery);
    $countStmt->execute($params);
    $totalPosts = $countStmt->fetch(PDO::FETCH_ASSOC)['count'];
    $totalPages = ceil($totalPosts / POSTS_PER_PAGE);
    
    // Get posts
    $query = "SELECT * FROM blog_posts $whereClause ORDER BY created_at DESC LIMIT ? OFFSET ?";
    $params[] = POSTS_PER_PAGE;
    $params[] = $offset;
    
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    $posts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get categories for filter
    $categoryQuery = "SELECT DISTINCT category FROM blog_posts WHERE status = 'published'";
    $categoryStmt = $db->prepare($categoryQuery);
    $categoryStmt->execute();
    $categories = $categoryStmt->fetchAll(PDO::FETCH_COLUMN);
    
} catch(Exception $e) {
    $posts = [];
    $categories = [];
    $totalPages = 1;
}

include 'includes/header.php';
?>

<!-- Page Header -->
<section class="section" style="background: #f8f9fa; padding: 2rem 0;">
    <div class="container">
        <h1 class="section-title">South Safari Blog</h1>
        <p class="section-subtitle">Insights, updates, and success stories from our developer partnership community</p>
    </div>
</section>

<!-- Category Filter -->
<?php if (!empty($categories)): ?>
<section class="section" style="padding: 1rem 0; background: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
    <div class="container">
        <div class="category-filter" style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
            <a href="blog.php" class="filter-btn <?php echo empty($category) ? 'active' : ''; ?>" 
               style="padding: 0.5rem 1rem; border-radius: 20px; text-decoration: none; background: <?php echo empty($category) ? '#667eea' : '#f0f0f0'; ?>; color: <?php echo empty($category) ? 'white' : '#333'; ?>;">
                All Posts
            </a>
            <?php foreach ($categories as $cat): ?>
                <a href="blog.php?category=<?php echo urlencode($cat); ?>" 
                   class="filter-btn <?php echo $category === $cat ? 'active' : ''; ?>"
                   style="padding: 0.5rem 1rem; border-radius: 20px; text-decoration: none; background: <?php echo $category === $cat ? '#667eea' : '#f0f0f0'; ?>; color: <?php echo $category === $cat ? 'white' : '#333'; ?>;">
                    <?php echo ucwords(str_replace('_', ' ', $cat)); ?>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Blog Posts -->
<section class="section">
    <div class="container">
        <?php if (empty($posts)): ?>
            <div class="text-center" style="padding: 3rem 0;">
                <i class="fas fa-blog" style="font-size: 3rem; color: #ccc; margin-bottom: 1rem;"></i>
                <h3>No Blog Posts Yet</h3>
                <p>We're working on creating valuable content for our developer community. Check back soon!</p>
                <a href="contact.php" class="btn btn-primary" style="margin-top: 1rem;">Subscribe for Updates</a>
            </div>
        <?php else: ?>
            <div class="blog-grid">
                <?php foreach ($posts as $post): ?>
                    <article class="card blog-post" style="margin-bottom: 2rem;">
                        <?php if (!empty($post['featured_image'])): ?>
                            <div class="post-image" style="height: 200px; background: #f0f0f0; border-radius: 10px 10px 0 0; margin-bottom: 1rem; overflow: hidden;">
                                <img src="uploads/blog/<?php echo htmlspecialchars($post['featured_image']); ?>" 
                                     alt="<?php echo htmlspecialchars($post['title']); ?>"
                                     style="width: 100%; height: 100%; object-fit: cover;">
                            </div>
                        <?php endif; ?>
                        
                        <div class="post-header">
                            <div class="post-meta" style="margin-bottom: 1rem;">
                                <span class="category-badge" style="background: #667eea; color: white; padding: 0.25rem 0.5rem; border-radius: 3px; font-size: 0.8rem;">
                                    <?php echo ucwords(str_replace('_', ' ', $post['category'])); ?>
                                </span>
                                <span style="color: #888; margin-left: 1rem;">
                                    <i class="fas fa-calendar"></i>
                                    <?php echo format_date($post['created_at']); ?>
                                </span>
                            </div>
                            
                            <h2 class="post-title" style="margin-bottom: 1rem;">
                                <a href="blog-post.php?slug=<?php echo urlencode($post['slug']); ?>" 
                                   style="color: #1a1a1a; text-decoration: none;">
                                    <?php echo htmlspecialchars($post['title']); ?>
                                </a>
                            </h2>
                        </div>
                        
                        <div class="post-content">
                            <p style="color: #666; line-height: 1.6;">
                                <?php 
                                if (!empty($post['excerpt'])) {
                                    echo htmlspecialchars($post['excerpt']);
                                } else {
                                    echo truncate_text(strip_tags($post['content']), 150);
                                }
                                ?>
                            </p>
                        </div>
                        
                        <div class="post-footer" style="margin-top: 1.5rem; padding-top: 1rem; border-top: 1px solid #eee;">
                            <a href="blog-post.php?slug=<?php echo urlencode($post['slug']); ?>" 
                               class="btn btn-primary">
                                Read More <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </article>
                <?php endforeach; ?>
            </div>
            
            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <div class="pagination" style="display: flex; justify-content: center; align-items: center; gap: 1rem; margin-top: 3rem;">
                    <?php if ($page > 1): ?>
                        <a href="blog.php?page=<?php echo $page - 1; ?><?php echo !empty($category) ? '&category=' . urlencode($category) : ''; ?>" 
                           class="btn btn-secondary">
                            <i class="fas fa-chevron-left"></i> Previous
                        </a>
                    <?php endif; ?>
                    
                    <span style="color: #666;">
                        Page <?php echo $page; ?> of <?php echo $totalPages; ?>
                    </span>
                    
                    <?php if ($page < $totalPages): ?>
                        <a href="blog.php?page=<?php echo $page + 1; ?><?php echo !empty($category) ? '&category=' . urlencode($category) : ''; ?>" 
                           class="btn btn-secondary">
                            Next <i class="fas fa-chevron-right"></i>
                        </a>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
</section>

<!-- Newsletter Signup -->
<section class="section" style="background: #1a1a1a; color: white;">
    <div class="container text-center">
        <h2 style="color: white; margin-bottom: 1rem;">Stay Updated</h2>
        <p style="color: #ccc; margin-bottom: 2rem;">Subscribe to our newsletter for the latest partnership opportunities and success stories</p>
        
        <form id="newsletter-form" style="max-width: 400px; margin: 0 auto; display: flex; gap: 1rem;">
            <input type="email" name="email" placeholder="Enter your email" required
                   style="flex: 1; padding: 0.75rem; border: none; border-radius: 5px;">
            <button type="submit" class="btn btn-primary">Subscribe</button>
        </form>
    </div>
</section>

<!-- Featured Categories -->
<section class="section" style="background: #f8f9fa;">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Explore Our Content</h2>
        </div>
        
        <div class="grid grid-3">
            <div class="card text-center">
                <i class="fas fa-rocket" style="font-size: 2rem; color: #667eea; margin-bottom: 1rem;"></i>
                <h3>Project Updates</h3>
                <p>Latest news and progress reports from our active partnerships and development projects.</p>
                <a href="blog.php?category=project_updates" class="btn btn-secondary">Read Updates</a>
            </div>
            
            <div class="card text-center">
                <i class="fas fa-user-tie" style="font-size: 2rem; color: #28a745; margin-bottom: 1rem;"></i>
                <h3>Developer Spotlights</h3>
                <p>Meet our talented developer partners and learn about their success stories and experiences.</p>
                <a href="blog.php?category=developer_spotlights" class="btn btn-secondary">Meet Developers</a>
            </div>
            
            <div class="card text-center">
                <i class="fas fa-chart-line" style="font-size: 2rem; color: #ffc107; margin-bottom: 1rem;"></i>
                <h3>Market Insights</h3>
                <p>Analysis and insights about the South African tech market and business opportunities.</p>
                <a href="blog.php?category=market_insights" class="btn btn-secondary">View Insights</a>
            </div>
        </div>
    </div>
</section>

<script>
document.getElementById('newsletter-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const email = this.email.value;
    const button = this.querySelector('button');
    const originalText = button.textContent;
    
    button.textContent = 'Subscribing...';
    button.disabled = true;
    
    // Simulate subscription (you can implement actual newsletter functionality)
    setTimeout(() => {
        alert('Thank you for subscribing! We\'ll keep you updated with the latest news.');
        this.reset();
        button.textContent = originalText;
        button.disabled = false;
    }, 1000);
});
</script>

<?php include 'includes/footer.php'; ?>
