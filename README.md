# South Safari Website

A professional partnership platform connecting skilled developers with South African business opportunities.

## 🚀 Quick Start

1. **Setup Database**: Visit `http://localhost/southsafari/setup.php` to initialize the database
2. **Access Website**: Visit `http://localhost/southsafari`
3. **Admin Dashboard**: Visit `http://localhost/southsafari/admin/login.php`
   - Default login: `admin` / `admin123`

## 📁 Project Structure

```
southsafari/
├── config/
│   ├── database.php      # Database connection
│   └── config.php        # Site configuration
├── includes/
│   ├── header.php        # Site header
│   └── footer.php        # Site footer
├── admin/
│   ├── login.php         # Admin login
│   ├── dashboard.php     # Admin dashboard
│   └── logout.php        # Admin logout
├── handlers/
│   ├── contact.php       # Contact form handler
│   └── application.php   # Application form handler
├── assets/
│   ├── css/              # Stylesheets
│   ├── js/               # JavaScript files
│   └── images/           # Images
├── uploads/              # File uploads directory
├── database/
│   └── schema.sql        # Database structure
├── index.php             # Homepage
├── projects.php          # Current projects page
├── partnerships.php      # Active partnerships page
├── blog.php              # Blog page
├── resources.php         # Developer resources page
├── contact.php           # Contact page
└── setup.php             # Database setup script
```

## 🌟 Features

### Public Website
- **Homepage**: Hero section, partnership model explanation, benefits, stats
- **Projects Page**: Current opportunities with search/filter, application modal
- **Partnerships Page**: Success stories and testimonials
- **Blog**: News, updates, and insights (ready for content)
- **Resources**: Developer guidelines, FAQ, application process
- **Contact**: Multiple contact methods (form, email, WhatsApp)

### Admin Dashboard
- **Dashboard Overview**: Statistics and recent activity
- **Project Management**: Add, edit, delete projects
- **Developer Management**: View applications and developer profiles
- **Message Management**: Handle contact form submissions
- **Blog Management**: Create and manage blog posts (basic structure)

### Communication Features
- Contact forms with validation
- Project application system
- WhatsApp integration
- Email notifications
- File upload for CVs/portfolios

## ⚙️ Configuration

### Database Settings
Edit `config/database.php`:
```php
private $host = 'localhost';
private $db_name = 'southsafari';
private $username = 'root';
private $password = '';
```

### Site Settings
Edit `config/config.php`:
```php
define('SITE_NAME', 'South Safari');
define('SITE_URL', 'http://localhost/southsafari');
define('SITE_EMAIL', '<EMAIL>');
define('WHATSAPP_NUMBER', '+27123456789'); // Update this!
```

## 📱 Responsive Design

The website is fully responsive and works on:
- Desktop computers
- Tablets
- Mobile phones
- All modern browsers

## 🎨 Design Features

- **Monochromatic Color Scheme**: Black, white, and grey tones
- **Professional Layout**: Clean and modern design
- **African Elements**: Subtle safari/mountain branding
- **Accessibility**: High contrast, keyboard navigation
- **Performance**: Optimized loading times

## 🔧 Customization

### Adding New Projects
1. Login to admin dashboard
2. Go to Projects section
3. Click "Add New Project"
4. Fill in project details and save

### Updating Content
- **Homepage**: Edit `index.php`
- **About/Resources**: Edit `resources.php`
- **Contact Info**: Update `config/config.php`

### Styling Changes
- **Main Styles**: `assets/css/style.css`
- **Responsive**: `assets/css/responsive.css`

## 📧 Email Configuration

To enable email notifications, configure your server's mail settings or update the email functions in:
- `handlers/contact.php`
- `handlers/application.php`

## 🔒 Security

### Important Security Steps
1. **Change Default Admin Password**: Login and update password
2. **Delete Setup File**: Remove `setup.php` after setup
3. **Update Database Credentials**: Use secure database passwords
4. **File Permissions**: Ensure proper file permissions on uploads directory

### Admin Access
- Default username: `admin`
- Default password: `admin123`
- **⚠️ Change this immediately after first login!**

## 📊 Database Tables

- `projects` - Project opportunities
- `developers` - Developer profiles and applications
- `project_applications` - Project applications
- `messages` - Contact form submissions
- `blog_posts` - Blog content
- `partnerships` - Success stories
- `admin_users` - Admin accounts

## 🚀 Going Live

### Before Deployment
1. Update `SITE_URL` in `config/config.php`
2. Update `WHATSAPP_NUMBER` with your actual number
3. Configure email settings for notifications
4. Change admin password
5. Delete `setup.php`
6. Add SSL certificate
7. Set up regular backups

### Hosting Requirements
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- SSL certificate (recommended)

## 📞 Support

### Contact Methods Implemented
- **Contact Form**: Comprehensive form with validation
- **Email**: Direct email links
- **WhatsApp**: Quick contact button
- **Application System**: Project-specific applications

### Admin Features
- View all messages and applications
- Respond to inquiries
- Manage developer database
- Track project applications
- Monitor website activity

## 🎯 Next Steps

1. **Content**: Add your actual projects and partnerships
2. **WhatsApp**: Update with your real WhatsApp number
3. **Email**: Configure email notifications
4. **Blog**: Start adding blog content
5. **SEO**: Add meta descriptions and optimize for search
6. **Analytics**: Add Google Analytics tracking
7. **Testing**: Test all forms and functionality

## 📝 Sample Data

The setup includes sample data:
- 3 sample projects
- 2 sample partnerships
- Default admin user

You can modify or delete this sample data through the admin dashboard.

---

**Built with**: PHP, MySQL, HTML5, CSS3, JavaScript
**Framework**: Custom lightweight framework
**Design**: Responsive, mobile-first approach
**Security**: Form validation, SQL injection protection, secure admin access
