<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' . SITE_NAME : SITE_NAME . ' - Developer Partnership Platform'; ?></title>
    <meta name="description" content="<?php echo isset($page_description) ? $page_description : 'Connect skilled developers with South African business opportunities through our partnership platform.'; ?>">
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body>
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <a href="index.php" class="logo">
                        <i class="fas fa-mountain"></i>
                        <span>South Safari</span>
                    </a>
                </div>
                
                <div class="nav-menu" id="nav-menu">
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="index.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>">Home</a>
                        </li>
                        <li class="nav-item">
                            <a href="projects.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'projects.php' ? 'active' : ''; ?>">Projects</a>
                        </li>
                        <li class="nav-item">
                            <a href="partnerships.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'partnerships.php' ? 'active' : ''; ?>">Partnerships</a>
                        </li>
                        <li class="nav-item">
                            <a href="blog.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'blog.php' ? 'active' : ''; ?>">Blog</a>
                        </li>
                        <li class="nav-item">
                            <a href="resources.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'resources.php' ? 'active' : ''; ?>">Resources</a>
                        </li>
                        <li class="nav-item">
                            <a href="contact.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'contact.php' ? 'active' : ''; ?>">Contact</a>
                        </li>
                    </ul>
                </div>
                
                <div class="nav-actions">
                    <a href="<?php echo WHATSAPP_LINK; ?>" class="whatsapp-btn" target="_blank" title="Contact us on WhatsApp">
                        <i class="fab fa-whatsapp"></i>
                    </a>
                    <button class="nav-toggle" id="nav-toggle">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                </div>
            </div>
        </nav>
    </header>

    <main class="main-content">
