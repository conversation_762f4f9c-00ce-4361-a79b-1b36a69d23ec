-- South Safari Database Schema
CREATE DATABASE IF NOT EXISTS southsafari;
USE southsafari;

-- Projects table
CREATE TABLE projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    skills_required TEXT NOT NULL,
    partnership_terms TEXT NOT NULL,
    status ENUM('seeking', 'in_development', 'launched', 'completed') DEFAULT 'seeking',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Developers table
CREATE TABLE developers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    country VARCHAR(100) NOT NULL,
    experience_level ENUM('junior', 'mid', 'senior', 'expert') NOT NULL,
    skills TEXT NOT NULL,
    portfolio_url VARCHAR(255),
    cv_file VARCHAR(255),
    status ENUM('active', 'inactive', 'blacklisted') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Project applications table
CREATE TABLE project_applications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    developer_id INT NOT NULL,
    message TEXT NOT NULL,
    status ENUM('pending', 'reviewed', 'accepted', 'rejected') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (developer_id) REFERENCES developers(id) ON DELETE CASCADE
);

-- Messages table
CREATE TABLE messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('general', 'project_inquiry', 'partnership') DEFAULT 'general',
    status ENUM('unread', 'read', 'replied') DEFAULT 'unread',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Blog posts table
CREATE TABLE blog_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    category ENUM('project_updates', 'partnerships', 'developer_spotlights', 'market_insights', 'success_stories') NOT NULL,
    status ENUM('draft', 'published') DEFAULT 'draft',
    featured_image VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Blog comments table
CREATE TABLE blog_comments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    post_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    comment TEXT NOT NULL,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE
);

-- Partnerships table (for showcase)
CREATE TABLE partnerships (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_name VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    developer_name VARCHAR(255) NOT NULL,
    developer_country VARCHAR(100) NOT NULL,
    project_url VARCHAR(255),
    achievements TEXT,
    testimonial TEXT,
    featured_image VARCHAR(255),
    status ENUM('active', 'completed', 'paused') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Admin users table
CREATE TABLE admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default admin user (password: admin123)
INSERT INTO admin_users (username, password, email) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>');

-- Sample data for testing
INSERT INTO projects (title, description, skills_required, partnership_terms) VALUES
('E-commerce Platform Development', 'Build a modern e-commerce platform for South African retail market', 'PHP, MySQL, JavaScript, Payment Integration', '60/40 revenue split, ongoing maintenance partnership'),
('Mobile App for Tourism', 'Create a mobile application for South African tourism industry', 'React Native, Node.js, MongoDB, GPS Integration', '50/50 partnership, shared development costs'),
('Financial Dashboard System', 'Develop a comprehensive financial management dashboard', 'Python, Django, PostgreSQL, Data Visualization', '70/30 revenue split, 6-month development timeline');

INSERT INTO partnerships (project_name, description, developer_name, developer_country, achievements, testimonial) VALUES
('Online Marketplace', 'Successfully launched marketplace connecting local artisans with international buyers', 'Ahmed Hassan', 'Pakistan', 'Generated $50K revenue in first 3 months, 500+ active users', 'Working with South Safari opened doors to the African market I never knew existed.'),
('Property Management System', 'Comprehensive property management solution for Cape Town real estate', 'Priya Sharma', 'India', 'Managing 200+ properties, 95% client satisfaction rate', 'The partnership model allowed me to focus on development while South Safari handled business development.');
