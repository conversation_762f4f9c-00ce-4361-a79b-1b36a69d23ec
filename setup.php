<?php
// South Safari Website Setup Script
// Run this file once to set up the database and initial data

require_once 'config/config.php';

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>South Safari Setup</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .success { color: green; background: #f0fff0; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: red; background: #fff0f0; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: blue; background: #f0f0ff; padding: 10px; border-radius: 5px; margin: 10px 0; }
        h1 { color: #333; }
        .step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>";

echo "<h1>South Safari Website Setup</h1>";

try {
    // Step 1: Create database connection
    echo "<div class='step'>";
    echo "<h2>Step 1: Database Connection</h2>";
    
    $database = new Database();
    $db = $database->getConnection();
    
    if ($db) {
        echo "<div class='success'>✓ Database connection successful!</div>";
    } else {
        throw new Exception("Failed to connect to database");
    }
    echo "</div>";
    
    // Step 2: Create database and tables
    echo "<div class='step'>";
    echo "<h2>Step 2: Creating Database Structure</h2>";
    
    // Read and execute SQL schema
    $sql = file_get_contents('database/schema.sql');
    $statements = explode(';', $sql);
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement)) {
            try {
                $db->exec($statement);
                echo "<div class='info'>Executed: " . substr($statement, 0, 50) . "...</div>";
            } catch (PDOException $e) {
                // Ignore errors for statements that might already exist
                if (strpos($e->getMessage(), 'already exists') === false) {
                    echo "<div class='error'>Error: " . $e->getMessage() . "</div>";
                }
            }
        }
    }
    
    echo "<div class='success'>✓ Database structure created successfully!</div>";
    echo "</div>";
    
    // Step 3: Create uploads directory
    echo "<div class='step'>";
    echo "<h2>Step 3: Creating Upload Directories</h2>";
    
    $uploadDir = UPLOAD_DIR;
    if (!is_dir($uploadDir)) {
        if (mkdir($uploadDir, 0755, true)) {
            echo "<div class='success'>✓ Created uploads directory: $uploadDir</div>";
        } else {
            echo "<div class='error'>✗ Failed to create uploads directory: $uploadDir</div>";
        }
    } else {
        echo "<div class='info'>✓ Uploads directory already exists: $uploadDir</div>";
    }
    
    // Create subdirectories
    $subDirs = ['cv', 'images', 'blog'];
    foreach ($subDirs as $subDir) {
        $fullPath = $uploadDir . $subDir;
        if (!is_dir($fullPath)) {
            if (mkdir($fullPath, 0755, true)) {
                echo "<div class='success'>✓ Created subdirectory: $fullPath</div>";
            } else {
                echo "<div class='error'>✗ Failed to create subdirectory: $fullPath</div>";
            }
        } else {
            echo "<div class='info'>✓ Subdirectory already exists: $fullPath</div>";
        }
    }
    echo "</div>";
    
    // Step 4: Verify admin user
    echo "<div class='step'>";
    echo "<h2>Step 4: Admin User Setup</h2>";
    
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM admin_users");
    $stmt->execute();
    $adminCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($adminCount > 0) {
        echo "<div class='success'>✓ Admin user already exists</div>";
        echo "<div class='info'>Default login: admin / admin123</div>";
    } else {
        // Create default admin user
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $db->prepare("INSERT INTO admin_users (username, password, email) VALUES (?, ?, ?)");
        $stmt->execute(['admin', $hashedPassword, ADMIN_EMAIL]);
        echo "<div class='success'>✓ Created default admin user</div>";
        echo "<div class='info'>Login: admin / admin123</div>";
    }
    echo "</div>";
    
    // Step 5: Sample data
    echo "<div class='step'>";
    echo "<h2>Step 5: Sample Data</h2>";
    
    // Check if sample data already exists
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM projects");
    $stmt->execute();
    $projectCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($projectCount == 0) {
        // Insert sample projects
        $sampleProjects = [
            [
                'title' => 'E-commerce Platform for Local Artisans',
                'description' => 'Build a comprehensive e-commerce platform connecting South African artisans with international buyers. Features include product catalog, payment integration, shipping management, and vendor dashboard.',
                'skills_required' => 'PHP, MySQL, JavaScript, Payment Gateway Integration, Responsive Design',
                'partnership_terms' => '60/40 revenue split, 6-month development timeline, ongoing maintenance partnership'
            ],
            [
                'title' => 'Tourism Mobile App Development',
                'description' => 'Create a mobile application for South African tourism featuring interactive maps, booking system, local guides, and cultural information.',
                'skills_required' => 'React Native, Node.js, MongoDB, GPS Integration, Payment Processing',
                'partnership_terms' => '50/50 partnership, shared development costs, revenue sharing from bookings'
            ],
            [
                'title' => 'Financial Dashboard for SMEs',
                'description' => 'Develop a comprehensive financial management dashboard for small and medium enterprises in South Africa, including invoicing, expense tracking, and reporting.',
                'skills_required' => 'Python, Django, PostgreSQL, Data Visualization, API Integration',
                'partnership_terms' => '70/30 revenue split, 4-month development timeline, SaaS model'
            ]
        ];
        
        foreach ($sampleProjects as $project) {
            $stmt = $db->prepare("INSERT INTO projects (title, description, skills_required, partnership_terms) VALUES (?, ?, ?, ?)");
            $stmt->execute([$project['title'], $project['description'], $project['skills_required'], $project['partnership_terms']]);
        }
        
        echo "<div class='success'>✓ Sample projects created</div>";
    } else {
        echo "<div class='info'>✓ Projects already exist in database</div>";
    }
    
    // Sample partnerships
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM partnerships");
    $stmt->execute();
    $partnershipCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($partnershipCount == 0) {
        $samplePartnerships = [
            [
                'project_name' => 'Online Marketplace Success',
                'description' => 'Successfully launched marketplace connecting local artisans with international buyers, generating significant revenue in the first quarter.',
                'developer_name' => 'Ahmed Hassan',
                'developer_country' => 'Pakistan',
                'achievements' => 'Generated $50K revenue in first 3 months, 500+ active users, 95% customer satisfaction',
                'testimonial' => 'Working with South Safari opened doors to the African market I never knew existed. The partnership model allowed me to focus on what I do best while they handled business development.'
            ],
            [
                'project_name' => 'Property Management System',
                'description' => 'Comprehensive property management solution for Cape Town real estate market, streamlining operations for multiple property management companies.',
                'developer_name' => 'Priya Sharma',
                'developer_country' => 'India',
                'achievements' => 'Managing 200+ properties, 95% client satisfaction rate, expanded to 3 cities',
                'testimonial' => 'The South Safari team provided incredible support throughout the project. Their understanding of the local market was invaluable for creating a solution that truly works.'
            ]
        ];
        
        foreach ($samplePartnerships as $partnership) {
            $stmt = $db->prepare("INSERT INTO partnerships (project_name, description, developer_name, developer_country, achievements, testimonial) VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->execute([$partnership['project_name'], $partnership['description'], $partnership['developer_name'], $partnership['developer_country'], $partnership['achievements'], $partnership['testimonial']]);
        }
        
        echo "<div class='success'>✓ Sample partnerships created</div>";
    } else {
        echo "<div class='info'>✓ Partnerships already exist in database</div>";
    }
    echo "</div>";
    
    // Step 6: Configuration check
    echo "<div class='step'>";
    echo "<h2>Step 6: Configuration Check</h2>";
    
    echo "<div class='info'><strong>Site Configuration:</strong></div>";
    echo "<div class='info'>Site Name: " . SITE_NAME . "</div>";
    echo "<div class='info'>Site URL: " . SITE_URL . "</div>";
    echo "<div class='info'>Site Email: " . SITE_EMAIL . "</div>";
    echo "<div class='info'>WhatsApp Number: " . WHATSAPP_NUMBER . "</div>";
    
    echo "<div class='success'>✓ Configuration looks good!</div>";
    echo "</div>";
    
    // Final success message
    echo "<div class='step' style='background: #f0fff0; border-color: #28a745;'>";
    echo "<h2 style='color: #28a745;'>🎉 Setup Complete!</h2>";
    echo "<p><strong>Your South Safari website is now ready!</strong></p>";
    echo "<p>Next steps:</p>";
    echo "<ul>";
    echo "<li>Visit your website: <a href='" . SITE_URL . "' target='_blank'>" . SITE_URL . "</a></li>";
    echo "<li>Access admin dashboard: <a href='" . SITE_URL . "/admin/login.php' target='_blank'>" . SITE_URL . "/admin/login.php</a></li>";
    echo "<li>Default admin login: <strong>admin</strong> / <strong>admin123</strong></li>";
    echo "<li>Update WhatsApp number in config/config.php</li>";
    echo "<li>Customize content and add your own projects</li>";
    echo "</ul>";
    echo "<p style='color: #666; font-size: 0.9rem;'><strong>Security Note:</strong> Please change the default admin password after first login and delete this setup.php file.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>Setup Error</h2>";
    echo "<p>An error occurred during setup: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration and try again.</p>";
    echo "</div>";
}

echo "</body></html>";
?>
