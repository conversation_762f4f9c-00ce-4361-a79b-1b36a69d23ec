<?php
session_start();
require_once '../config/config.php';

// Check if admin is logged in
if (!is_admin_logged_in()) {
    redirect('login.php');
}

// Get dashboard statistics
try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Get counts
    $stats = [];
    
    // Total projects
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM projects");
    $stmt->execute();
    $stats['total_projects'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // Active projects
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM projects WHERE status = 'seeking'");
    $stmt->execute();
    $stats['active_projects'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // Total developers
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM developers");
    $stmt->execute();
    $stats['total_developers'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // Unread messages
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM messages WHERE status = 'unread'");
    $stmt->execute();
    $stats['unread_messages'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // Pending applications
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM project_applications WHERE status = 'pending'");
    $stmt->execute();
    $stats['pending_applications'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // Recent messages
    $stmt = $db->prepare("SELECT * FROM messages ORDER BY created_at DESC LIMIT 5");
    $stmt->execute();
    $recent_messages = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Recent applications
    $stmt = $db->prepare("
        SELECT pa.*, p.title as project_title, d.name as developer_name, d.email as developer_email 
        FROM project_applications pa 
        JOIN projects p ON pa.project_id = p.id 
        JOIN developers d ON pa.developer_id = d.id 
        ORDER BY pa.created_at DESC 
        LIMIT 5
    ");
    $stmt->execute();
    $recent_applications = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $stats = [
        'total_projects' => 0,
        'active_projects' => 0,
        'total_developers' => 0,
        'unread_messages' => 0,
        'pending_applications' => 0
    ];
    $recent_messages = [];
    $recent_applications = [];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - South Safari</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .admin-header {
            background: #1a1a1a;
            color: white;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .admin-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .admin-nav h1 {
            color: white;
            margin: 0;
            font-size: 1.5rem;
        }
        
        .admin-nav .nav-links {
            display: flex;
            gap: 2rem;
            align-items: center;
        }
        
        .admin-nav .nav-links a {
            color: #ccc;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .admin-nav .nav-links a:hover {
            color: white;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }
        
        .stat-card {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-card i {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        .stat-card h3 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
            color: #1a1a1a;
        }
        
        .stat-card p {
            color: #666;
            margin: 0;
        }
        
        .recent-section {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .recent-section h3 {
            margin-bottom: 1.5rem;
            color: #1a1a1a;
        }
        
        .recent-item {
            padding: 1rem;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }
        
        .recent-item:last-child {
            border-bottom: none;
        }
        
        .recent-item-content {
            flex: 1;
        }
        
        .recent-item-meta {
            color: #888;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }
        
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-unread {
            background: #fee;
            color: #c33;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .action-btn {
            display: block;
            padding: 1rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            text-align: center;
            transition: background 0.3s ease;
        }
        
        .action-btn:hover {
            background: #5a6fd8;
            color: white;
        }
    </style>
</head>
<body>
    <header class="admin-header">
        <div class="container">
            <nav class="admin-nav">
                <h1><i class="fas fa-mountain"></i> South Safari Admin</h1>
                <div class="nav-links">
                    <a href="projects.php">Projects</a>
                    <a href="developers.php">Developers</a>
                    <a href="applications.php">Applications</a>
                    <a href="messages.php">Messages</a>
                    <a href="../index.php" target="_blank">View Site</a>
                    <a href="logout.php">Logout</a>
                </div>
            </nav>
        </div>
    </header>

    <main style="padding: 2rem 0;">
        <div class="container">
            <h2>Dashboard Overview</h2>
            <p style="color: #666; margin-bottom: 2rem;">Welcome back, <?php echo htmlspecialchars($_SESSION['admin_username']); ?>!</p>
            
            <!-- Statistics Cards -->
            <div class="dashboard-grid">
                <div class="stat-card">
                    <i class="fas fa-project-diagram" style="color: #667eea;"></i>
                    <h3><?php echo $stats['total_projects']; ?></h3>
                    <p>Total Projects</p>
                </div>
                
                <div class="stat-card">
                    <i class="fas fa-search" style="color: #28a745;"></i>
                    <h3><?php echo $stats['active_projects']; ?></h3>
                    <p>Active Projects</p>
                </div>
                
                <div class="stat-card">
                    <i class="fas fa-users" style="color: #17a2b8;"></i>
                    <h3><?php echo $stats['total_developers']; ?></h3>
                    <p>Registered Developers</p>
                </div>
                
                <div class="stat-card">
                    <i class="fas fa-envelope" style="color: #ffc107;"></i>
                    <h3><?php echo $stats['unread_messages']; ?></h3>
                    <p>Unread Messages</p>
                </div>
                
                <div class="stat-card">
                    <i class="fas fa-file-alt" style="color: #dc3545;"></i>
                    <h3><?php echo $stats['pending_applications']; ?></h3>
                    <p>Pending Applications</p>
                </div>
            </div>
            
            <!-- Recent Messages -->
            <div class="recent-section">
                <h3>Recent Messages</h3>
                <?php if (empty($recent_messages)): ?>
                    <p style="color: #666; text-align: center; padding: 2rem;">No recent messages</p>
                <?php else: ?>
                    <?php foreach ($recent_messages as $message): ?>
                        <div class="recent-item">
                            <div class="recent-item-content">
                                <strong><?php echo htmlspecialchars($message['name']); ?></strong>
                                <span style="color: #666;"> - <?php echo htmlspecialchars($message['subject']); ?></span>
                                <p style="margin: 0.5rem 0; color: #666;">
                                    <?php echo truncate_text($message['message'], 100); ?>
                                </p>
                                <div class="recent-item-meta">
                                    <?php echo htmlspecialchars($message['email']); ?> • 
                                    <?php echo format_date($message['created_at']); ?>
                                </div>
                            </div>
                            <span class="status-badge status-<?php echo $message['status']; ?>">
                                <?php echo ucfirst($message['status']); ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
            
            <!-- Recent Applications -->
            <div class="recent-section">
                <h3>Recent Project Applications</h3>
                <?php if (empty($recent_applications)): ?>
                    <p style="color: #666; text-align: center; padding: 2rem;">No recent applications</p>
                <?php else: ?>
                    <?php foreach ($recent_applications as $application): ?>
                        <div class="recent-item">
                            <div class="recent-item-content">
                                <strong><?php echo htmlspecialchars($application['developer_name']); ?></strong>
                                <span style="color: #666;"> applied for </span>
                                <strong><?php echo htmlspecialchars($application['project_title']); ?></strong>
                                <p style="margin: 0.5rem 0; color: #666;">
                                    <?php echo truncate_text($application['message'], 100); ?>
                                </p>
                                <div class="recent-item-meta">
                                    <?php echo htmlspecialchars($application['developer_email']); ?> • 
                                    <?php echo format_date($application['created_at']); ?>
                                </div>
                            </div>
                            <span class="status-badge status-<?php echo $application['status']; ?>">
                                <?php echo ucfirst($application['status']); ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
            
            <!-- Quick Actions -->
            <div class="quick-actions">
                <a href="projects.php?action=add" class="action-btn">
                    <i class="fas fa-plus"></i> Add New Project
                </a>
                <a href="all-projects.php" class="action-btn">
                    <i class="fas fa-project-diagram"></i> View All Projects
                </a>
                <a href="applications.php" class="action-btn">
                    <i class="fas fa-paper-plane"></i> View Applications
                </a>
                <a href="messages.php" class="action-btn">
                    <i class="fas fa-envelope"></i> View All Messages
                </a>
                <a href="developers.php" class="action-btn">
                    <i class="fas fa-users"></i> Manage Developers
                </a>
                <a href="blog.php?action=add" class="action-btn">
                    <i class="fas fa-edit"></i> Write Blog Post
                </a>
            </div>
        </div>
    </main>
</body>
</html>
