<?php
session_start();
require_once '../config/config.php';

// Check if admin is logged in
if (!is_admin_logged_in()) {
    redirect('login.php');
}

$message = '';
$action = $_GET['action'] ?? 'list';

// For now, we'll create a simple placeholder since blog functionality 
// would require additional database tables
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog Management - South Safari Admin</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .admin-header {
            background: #1a1a1a;
            color: white;
            padding: 1rem 0;
        }
        .admin-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 2rem;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .nav-links a:hover {
            background: rgba(255,255,255,0.1);
        }
        .admin-main {
            padding: 2rem 0;
            min-height: calc(100vh - 200px);
        }
        .coming-soon {
            text-align: center;
            padding: 4rem 2rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .coming-soon i {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 2rem;
        }
        .coming-soon h2 {
            color: #1a1a1a;
            margin-bottom: 1rem;
        }
        .coming-soon p {
            color: #666;
            font-size: 1.1rem;
            max-width: 600px;
            margin: 0 auto 2rem;
            line-height: 1.6;
        }
        .feature-list {
            text-align: left;
            max-width: 500px;
            margin: 2rem auto;
        }
        .feature-list li {
            padding: 0.5rem 0;
            color: #555;
        }
        .feature-list i {
            color: #28a745;
            margin-right: 0.5rem;
        }
        .btn {
            padding: 0.75rem 2rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            display: inline-block;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <header class="admin-header">
        <div class="container">
            <nav class="admin-nav">
                <h1><i class="fas fa-mountain"></i> South Safari Admin</h1>
                <div class="nav-links">
                    <a href="dashboard.php">Dashboard</a>
                    <a href="projects.php">Projects</a>
                    <a href="developers.php">Developers</a>
                    <a href="messages.php">Messages</a>
                    <a href="../index.php" target="_blank">View Site</a>
                    <a href="logout.php">Logout</a>
                </div>
            </nav>
        </div>
    </header>

    <main class="admin-main">
        <div class="container">
            <div class="coming-soon">
                <i class="fas fa-blog"></i>
                <h2>Blog Management</h2>
                <p>
                    The blog management system is coming soon! This feature will allow you to create, 
                    edit, and manage blog posts to share insights about your projects, development 
                    tips, and South African tech industry news.
                </p>
                
                <h3 style="color: #333; margin-bottom: 1rem;">Planned Features:</h3>
                <ul class="feature-list">
                    <li><i class="fas fa-check"></i> Create and edit blog posts with rich text editor</li>
                    <li><i class="fas fa-check"></i> Add images and media to posts</li>
                    <li><i class="fas fa-check"></i> Categorize posts by topics</li>
                    <li><i class="fas fa-check"></i> Schedule posts for future publication</li>
                    <li><i class="fas fa-check"></i> SEO optimization tools</li>
                    <li><i class="fas fa-check"></i> Comment management system</li>
                    <li><i class="fas fa-check"></i> Social media sharing integration</li>
                </ul>
                
                <a href="dashboard.php" class="btn">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
            </div>
        </div>
    </main>
</body>
</html>
