<?php
require_once 'config/config.php';

echo "<h2>🔍 Login Debug Information</h2>";

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Check database connection
    echo "<p>✅ Database connection: OK</p>";
    
    // Check if admin_users table exists
    $stmt = $db->query("SHOW TABLES LIKE 'admin_users'");
    if ($stmt->rowCount() > 0) {
        echo "<p>✅ admin_users table: EXISTS</p>";
        
        // Check admin user
        $stmt = $db->prepare("SELECT id, username, email, created_at FROM admin_users WHERE username = 'admin'");
        $stmt->execute();
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($admin) {
            echo "<p>✅ Admin user exists:</p>";
            echo "<ul>";
            echo "<li>ID: " . $admin['id'] . "</li>";
            echo "<li>Username: " . $admin['username'] . "</li>";
            echo "<li>Email: " . $admin['email'] . "</li>";
            echo "<li>Created: " . $admin['created_at'] . "</li>";
            echo "</ul>";
            
            // Test password
            $stmt = $db->prepare("SELECT password FROM admin_users WHERE username = 'admin'");
            $stmt->execute();
            $storedPassword = $stmt->fetch(PDO::FETCH_ASSOC)['password'];
            
            echo "<p>🔐 Password hash: " . substr($storedPassword, 0, 20) . "...</p>";
            
            if (password_verify('admin123', $storedPassword)) {
                echo "<p style='color: green;'>✅ Password 'admin123' verification: PASSED</p>";
            } else {
                echo "<p style='color: red;'>❌ Password 'admin123' verification: FAILED</p>";
            }
            
        } else {
            echo "<p style='color: red;'>❌ Admin user not found</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ admin_users table: NOT FOUND</p>";
    }
    
    // Check session
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    echo "<p>📋 Session status: " . (session_status() == PHP_SESSION_ACTIVE ? "ACTIVE" : "INACTIVE") . "</p>";
    echo "<p>📋 Session ID: " . session_id() . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='admin/login.php'>Go to Admin Login</a></p>";
echo "<p><a href='reset_admin.php'>Reset Admin Password</a></p>";
?>
