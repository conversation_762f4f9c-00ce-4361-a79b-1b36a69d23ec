<?php
require_once 'config/config.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Check if admin user exists
    $stmt = $db->prepare("SELECT id, username FROM admin_users WHERE username = 'admin'");
    $stmt->execute();
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($admin) {
        // Update the admin password
        $newPassword = 'admin123';
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        
        $stmt = $db->prepare("UPDATE admin_users SET password = ? WHERE username = 'admin'");
        $stmt->execute([$hashedPassword]);
        
        echo "<h2>✅ Admin Password Reset Successful!</h2>";
        echo "<p><strong>Username:</strong> admin</p>";
        echo "<p><strong>Password:</strong> admin123</p>";
        echo "<p><a href='admin/login.php'>Go to Admin Login</a></p>";
        
        // Test the password verification
        $stmt = $db->prepare("SELECT password FROM admin_users WHERE username = 'admin'");
        $stmt->execute();
        $storedPassword = $stmt->fetch(PDO::FETCH_ASSOC)['password'];
        
        if (password_verify('admin123', $storedPassword)) {
            echo "<p style='color: green;'>✅ Password verification test: PASSED</p>";
        } else {
            echo "<p style='color: red;'>❌ Password verification test: FAILED</p>";
        }
        
    } else {
        // Create admin user if doesn't exist
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $db->prepare("INSERT INTO admin_users (username, password, email) VALUES (?, ?, ?)");
        $stmt->execute(['admin', $hashedPassword, '<EMAIL>']);
        
        echo "<h2>✅ Admin User Created!</h2>";
        echo "<p><strong>Username:</strong> admin</p>";
        echo "<p><strong>Password:</strong> admin123</p>";
        echo "<p><a href='admin/login.php'>Go to Admin Login</a></p>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>
