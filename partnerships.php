<?php
require_once 'config/config.php';

$page_title = 'Active Partnerships';
$page_description = 'Discover successful partnerships and collaborations between South Safari and skilled developers from around the world.';

// Get partnerships from database
try {
    $database = new Database();
    $db = $database->getConnection();
    
    $query = "SELECT * FROM partnerships WHERE status IN ('active', 'completed') ORDER BY created_at DESC";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $partnerships = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch(Exception $e) {
    $partnerships = [];
}

include 'includes/header.php';
?>

<!-- Page Header -->
<section class="section" style="background: #f8f9fa; padding: 2rem 0;">
    <div class="container">
        <h1 class="section-title">Active Partnerships & Success Stories</h1>
        <p class="section-subtitle">See how our partnership model creates successful collaborations between developers and South African businesses</p>
    </div>
</section>

<!-- Partnership Stats -->
<section class="section stats">
    <div class="container">
        <div class="stats-grid">
            <div class="stat-item">
                <h3><?php echo count($partnerships); ?>+</h3>
                <p>Successful Partnerships</p>
            </div>
            <div class="stat-item">
                <h3>15+</h3>
                <p>Countries Represented</p>
            </div>
            <div class="stat-item">
                <h3>$2M+</h3>
                <p>Revenue Generated</p>
            </div>
            <div class="stat-item">
                <h3>95%</h3>
                <p>Partner Satisfaction</p>
            </div>
        </div>
    </div>
</section>

<!-- Partnerships Showcase -->
<section class="section">
    <div class="container">
        <?php if (empty($partnerships)): ?>
            <div class="text-center" style="padding: 3rem 0;">
                <i class="fas fa-handshake" style="font-size: 3rem; color: #ccc; margin-bottom: 1rem;"></i>
                <h3>Partnerships Coming Soon</h3>
                <p>We're currently building our first partnerships. Check back soon to see our success stories!</p>
                <a href="contact.php" class="btn btn-primary" style="margin-top: 1rem;">Become Our First Partner</a>
            </div>
        <?php else: ?>
            <div class="grid grid-2">
                <?php foreach ($partnerships as $partnership): ?>
                    <div class="card partnership-card" style="margin-bottom: 2rem;">
                        <?php if (!empty($partnership['featured_image'])): ?>
                            <div class="partnership-image" style="height: 200px; background: #f0f0f0; border-radius: 10px 10px 0 0; margin-bottom: 1rem;">
                                <img src="uploads/<?php echo htmlspecialchars($partnership['featured_image']); ?>" 
                                     alt="<?php echo htmlspecialchars($partnership['project_name']); ?>"
                                     style="width: 100%; height: 100%; object-fit: cover; border-radius: 10px 10px 0 0;">
                            </div>
                        <?php endif; ?>
                        
                        <div class="card-header">
                            <h3 class="card-title"><?php echo htmlspecialchars($partnership['project_name']); ?></h3>
                            <div class="card-meta">
                                <span class="partnership-status">
                                    <i class="fas fa-circle" style="color: <?php echo $partnership['status'] === 'active' ? '#28a745' : '#007bff'; ?>; font-size: 0.5rem;"></i>
                                    <?php echo ucfirst($partnership['status']); ?>
                                </span>
                                <span style="margin-left: 1rem; color: #888;">
                                    <i class="fas fa-calendar"></i>
                                    <?php echo format_date($partnership['created_at']); ?>
                                </span>
                            </div>
                        </div>
                        
                        <div class="card-content">
                            <p><?php echo htmlspecialchars($partnership['description']); ?></p>
                            
                            <div style="margin: 1.5rem 0;">
                                <div class="developer-info" style="display: flex; align-items: center; padding: 1rem; background: #f8f9fa; border-radius: 8px;">
                                    <div class="developer-avatar" style="width: 50px; height: 50px; background: #667eea; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; margin-right: 1rem;">
                                        <?php echo strtoupper(substr($partnership['developer_name'], 0, 2)); ?>
                                    </div>
                                    <div>
                                        <strong><?php echo htmlspecialchars($partnership['developer_name']); ?></strong>
                                        <p style="margin: 0; color: #666; font-size: 0.9rem;">
                                            <i class="fas fa-map-marker-alt"></i>
                                            <?php echo htmlspecialchars($partnership['developer_country']); ?>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <?php if (!empty($partnership['achievements'])): ?>
                                <div style="margin: 1rem 0;">
                                    <strong>Key Achievements:</strong>
                                    <p style="margin-top: 0.5rem; color: #666;">
                                        <?php echo htmlspecialchars($partnership['achievements']); ?>
                                    </p>
                                </div>
                            <?php endif; ?>
                            
                            <?php if (!empty($partnership['testimonial'])): ?>
                                <div style="margin: 1.5rem 0; padding: 1rem; background: #f8f9fa; border-left: 4px solid #667eea; border-radius: 0 8px 8px 0;">
                                    <p style="font-style: italic; margin: 0; color: #555;">
                                        "<?php echo htmlspecialchars($partnership['testimonial']); ?>"
                                    </p>
                                    <p style="margin: 0.5rem 0 0 0; font-weight: 500; color: #333;">
                                        - <?php echo htmlspecialchars($partnership['developer_name']); ?>
                                    </p>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="card-footer">
                            <?php if (!empty($partnership['project_url'])): ?>
                                <a href="<?php echo htmlspecialchars($partnership['project_url']); ?>" 
                                   target="_blank" class="btn btn-primary">
                                    <i class="fas fa-external-link-alt"></i> View Live Project
                                </a>
                            <?php endif; ?>
                            <button class="btn btn-secondary" onclick="sharePartnership('<?php echo htmlspecialchars($partnership['project_name']); ?>')">
                                <i class="fas fa-share"></i> Share
                            </button>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Success Metrics -->
<section class="section" style="background: #f8f9fa;">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">Partnership Success Metrics</h2>
            <p class="section-subtitle">Real results from our developer partnerships</p>
        </div>
        
        <div class="grid grid-3">
            <div class="card text-center">
                <i class="fas fa-chart-line" style="font-size: 2rem; color: #28a745; margin-bottom: 1rem;"></i>
                <h3>Revenue Growth</h3>
                <p>Average 150% revenue increase for partner developers within 6 months of joining our platform.</p>
            </div>
            
            <div class="card text-center">
                <i class="fas fa-clock" style="font-size: 2rem; color: #007bff; margin-bottom: 1rem;"></i>
                <h3>Project Timeline</h3>
                <p>Most projects launch within 3-6 months, with ongoing revenue sharing for successful partnerships.</p>
            </div>
            
            <div class="card text-center">
                <i class="fas fa-globe-africa" style="font-size: 2rem; color: #ffc107; margin-bottom: 1rem;"></i>
                <h3>Market Access</h3>
                <p>Direct access to South African market worth over $4 billion in digital services annually.</p>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="section" style="background: #1a1a1a; color: white;">
    <div class="container text-center">
        <h2 style="color: white; margin-bottom: 1rem;">Ready to Create Your Success Story?</h2>
        <p style="color: #ccc; font-size: 1.2rem; margin-bottom: 2rem;">Join our community of successful developer partners</p>
        <div class="cta-buttons">
            <a href="projects.php" class="btn btn-primary">View Current Projects</a>
            <a href="contact.php" class="btn btn-secondary">Apply for Partnership</a>
        </div>
    </div>
</section>

<script>
function sharePartnership(projectName) {
    if (navigator.share) {
        navigator.share({
            title: 'South Safari Partnership: ' + projectName,
            text: 'Check out this successful partnership between South Safari and a skilled developer!',
            url: window.location.href
        });
    } else {
        // Fallback for browsers that don't support Web Share API
        const url = window.location.href;
        navigator.clipboard.writeText(url).then(() => {
            alert('Partnership URL copied to clipboard!');
        });
    }
}
</script>

<?php include 'includes/footer.php'; ?>
