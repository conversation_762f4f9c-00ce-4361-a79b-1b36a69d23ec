<?php
session_start();
require_once '../config/config.php';

// Check if admin is logged in
if (!is_admin_logged_in()) {
    redirect('login.php');
}

try {
    $database = new Database();
    $db = $database->getConnection();
    
    // Handle actions
    $message = '';
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['action'];
        
        if ($action === 'delete') {
            $id = (int)$_POST['id'];
            $stmt = $db->prepare("DELETE FROM developers WHERE id = ?");
            $stmt->execute([$id]);
            $message = "Developer deleted successfully!";
        } elseif ($action === 'update_status') {
            $id = (int)$_POST['id'];
            $status = sanitize_input($_POST['status']);
            $stmt = $db->prepare("UPDATE developers SET status = ? WHERE id = ?");
            $stmt->execute([$status, $id]);
            $message = "Developer status updated successfully!";
        }
    }
    
    // Get developers
    $stmt = $db->prepare("SELECT * FROM developers ORDER BY created_at DESC");
    $stmt->execute();
    $developers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get developer applications count
    $developer_apps = [];
    foreach ($developers as $dev) {
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM project_applications WHERE developer_id = ?");
        $stmt->execute([$dev['id']]);
        $developer_apps[$dev['id']] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    }
    
} catch (Exception $e) {
    $message = "Error: " . $e->getMessage();
    $developers = [];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Developers - South Safari Admin</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .admin-header {
            background: #1a1a1a;
            color: white;
            padding: 1rem 0;
        }
        .admin-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 2rem;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .nav-links a:hover, .nav-links a.active {
            background: rgba(255,255,255,0.1);
        }
        .admin-main {
            padding: 2rem 0;
            min-height: calc(100vh - 200px);
        }
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }
        .developers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
        }
        .developer-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        .developer-card:hover {
            transform: translateY(-5px);
        }
        .developer-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        .developer-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: #667eea;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
            margin-right: 1rem;
        }
        .developer-info h3 {
            margin: 0 0 0.25rem 0;
            color: #1a1a1a;
        }
        .developer-info p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }
        .developer-skills {
            margin: 1rem 0;
        }
        .skill-tag {
            display: inline-block;
            background: #f0f0f0;
            color: #333;
            padding: 0.25rem 0.5rem;
            border-radius: 15px;
            font-size: 0.8rem;
            margin: 0.25rem 0.25rem 0.25rem 0;
        }
        .developer-stats {
            display: flex;
            justify-content: space-between;
            margin: 1rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .stat {
            text-align: center;
        }
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            font-size: 0.8rem;
            color: #666;
        }
        .developer-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 0.9rem;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        .message {
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1rem;
        }
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .search-box {
            margin-bottom: 2rem;
        }
        .search-box input {
            width: 100%;
            max-width: 400px;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 1rem;
        }
    </style>
</head>
<body>
    <header class="admin-header">
        <div class="container">
            <nav class="admin-nav">
                <h1><i class="fas fa-mountain"></i> South Safari Admin</h1>
                <div class="nav-links">
                    <a href="dashboard.php">Dashboard</a>
                    <a href="projects.php">Projects</a>
                    <a href="developers.php" class="active">Developers</a>
                    <a href="messages.php">Messages</a>
                    <a href="../index.php" target="_blank">View Site</a>
                    <a href="logout.php">Logout</a>
                </div>
            </nav>
        </div>
    </header>

    <main class="admin-main">
        <div class="container">
            <?php if ($message): ?>
                <div class="message <?php echo strpos($message, 'Error') === 0 ? 'error' : 'success'; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <div class="page-header">
                <h2>Manage Developers (<?php echo count($developers); ?>)</h2>
            </div>

            <div class="search-box">
                <input type="text" id="searchDevelopers" placeholder="Search developers by name, email, or skills..." onkeyup="filterDevelopers()">
            </div>

            <div class="developers-grid" id="developersGrid">
                <?php foreach ($developers as $developer): ?>
                    <div class="developer-card" data-name="<?php echo strtolower($developer['name']); ?>" 
                         data-email="<?php echo strtolower($developer['email']); ?>" 
                         data-skills="<?php echo strtolower($developer['skills']); ?>">
                        <div class="developer-header">
                            <div class="developer-avatar">
                                <?php echo strtoupper(substr($developer['name'], 0, 1)); ?>
                            </div>
                            <div class="developer-info">
                                <h3><?php echo htmlspecialchars($developer['name']); ?></h3>
                                <p><?php echo htmlspecialchars($developer['email']); ?></p>
                                <span class="status-badge status-<?php echo $developer['status']; ?>">
                                    <?php echo ucfirst($developer['status']); ?>
                                </span>
                            </div>
                        </div>

                        <div class="developer-skills">
                            <strong>Skills:</strong><br>
                            <?php 
                            $skills = explode(',', $developer['skills']);
                            foreach ($skills as $skill): 
                            ?>
                                <span class="skill-tag"><?php echo htmlspecialchars(trim($skill)); ?></span>
                            <?php endforeach; ?>
                        </div>

                        <div class="developer-stats">
                            <div class="stat">
                                <div class="stat-number"><?php echo $developer_apps[$developer['id']] ?? 0; ?></div>
                                <div class="stat-label">Applications</div>
                            </div>
                            <div class="stat">
                                <div class="stat-number"><?php echo $developer['experience_years']; ?></div>
                                <div class="stat-label">Years Exp.</div>
                            </div>
                        </div>

                        <p><strong>Bio:</strong> <?php echo htmlspecialchars(truncate_text($developer['bio'], 100)); ?></p>

                        <div class="developer-actions">
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="update_status">
                                <input type="hidden" name="id" value="<?php echo $developer['id']; ?>">
                                <select name="status" onchange="this.form.submit()" class="btn">
                                    <option value="active" <?php echo $developer['status'] === 'active' ? 'selected' : ''; ?>>Active</option>
                                    <option value="inactive" <?php echo $developer['status'] === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                    <option value="pending" <?php echo $developer['status'] === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                </select>
                            </form>
                            
                            <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this developer?');">
                                <input type="hidden" name="action" value="delete">
                                <input type="hidden" name="id" value="<?php echo $developer['id']; ?>">
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>

                        <div style="margin-top: 1rem; font-size: 0.8rem; color: #666;">
                            Joined: <?php echo format_date($developer['created_at']); ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <?php if (empty($developers)): ?>
                <div style="text-align: center; padding: 3rem; color: #666;">
                    <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                    <h3>No Developers Yet</h3>
                    <p>Developers will appear here once they register on your website.</p>
                </div>
            <?php endif; ?>
        </div>
    </main>

    <script>
        function filterDevelopers() {
            const searchTerm = document.getElementById('searchDevelopers').value.toLowerCase();
            const cards = document.querySelectorAll('.developer-card');
            
            cards.forEach(card => {
                const name = card.getAttribute('data-name');
                const email = card.getAttribute('data-email');
                const skills = card.getAttribute('data-skills');
                
                if (name.includes(searchTerm) || email.includes(searchTerm) || skills.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>
